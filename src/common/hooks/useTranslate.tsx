import { useEffect, useCallback } from 'react'
import { message } from 'antd/es'
import { MessageType } from '@src/common/const'
import { ipConnectivity } from '@src/common/utils'
import { log } from '@ht/xlog'
import type { MessageInstance } from 'antd/es/message/interface'

// 统一的useTranslate hook，支持两种使用模式
interface UseTranslateProps {
  // 可选参数，用于UI交互模式和API模式
  chatUiRef?: React.RefObject<{
    chatContext?: {
      onSend: (type: string, val: string, payload?: object, transformedFiles?: any[]) => boolean | undefined;
      [key: string]: any;
    };
    [key: string]: any;
  }>
  messageApi?: MessageInstance
}

interface UseTranslateReturn {
  // API模式返回的contextHolder
  contextHolder?: React.ReactNode
  // UI交互模式返回的handleTranslate函数
  handleTranslate?: (pageTitle: string) => Promise<void>
}

const useTranslate = (props?: UseTranslateProps): UseTranslateReturn => {
  const [internalMessageApi, contextHolder] = message.useMessage()
  const messageApiToUse = props?.messageApi || internalMessageApi

  const getUserId = () => {
    // 由于移除了登录功能，这里返回一个默认用户ID
    return "anonymous_user"
  }

  // 创建会话
  const createConversation = async (sendResponse: any) => {
    try {
      const response = await fetch(
        `http://10.102.92.209:9607/ai/orchestration/session/createSession`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            UserID: getUserId(),
            appId: 'web-assistant'
          }),
        }
      )

      const res = await response.json()
      console.log('创建会话的回调', res);
      if (res.code === '0') {
        const conversationID = res?.resultData?.conversationId

        if (conversationID) {
          sendResponse({
            code: '0',
            conversationID: conversationID,
          })
        } else {
          sendResponse({
            code: '1',
            error: '会话创建失败',
          })
        }
      }


    } catch (error) {
      console.log('创建会话错误:', error)
      sendResponse({
        code: '1',
        error: error.message,
      })
    }
  }

  // 获取当前标签页
  const getCurrentTab = useCallback(async (messageApi: MessageInstance) => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      return tabs[0]
    } catch (error) {
      console.error('获取当前标签页失败:', error)
      messageApi.error('获取当前标签页失败')
      return null
    }
  }, [])

  // 检查 content script 是否已注入
  const checkContentScriptInjected = useCallback(async (tabId: number): Promise<boolean> => {
    try {
      return await new Promise((resolve) => {
        chrome.tabs.sendMessage(
          tabId,
          { type: MessageType.CHECK_CONTENT_SCRIPT },
          (_response) => {
            if (chrome.runtime.lastError) {
              resolve(false)
            } else {
              resolve(true)
            }
          }
        )
      })
    } catch (error) {
      console.error('检查content script注入失败:', error)
      return false
    }
  }, [])

  // 刷新当前页面
  const refreshCurrentPage = useCallback(async (tabId: number) => {
    try {
      await chrome.tabs.reload(tabId)
    } catch (error) {
      console.error('刷新页面失败:', error)
    }
  }, [])

  // 记录日志
  const reportLog = useCallback((pageTitle: string) => {
    log({
      id: 'button_click',
      page_id: 'quickReplyBtn',
      page_title: pageTitle,
    })
  }, [])

  // 处理翻译请求 - 使用 chatUiRef.current.chatContext.onSend 方法
  const handleTranslateAPI = async (query: any, conversationID: any, sendResponse: any) => {
    try {
      // 检查 chatUiRef 是否存在
      if (!props?.chatUiRef?.current) {
        console.error('chatUiRef 未准备就绪')
        sendResponse({
          code: '1',
          error: 'chatUiRef 未准备就绪',
        })
        return
      }

      // 检查 chatContext 是否存在
      const chatContext = props.chatUiRef.current.chatContext
      if (!chatContext) {
        console.error('chatContext 未准备就绪')
        sendResponse({
          code: '1',
          error: 'chatContext 未准备就绪',
        })
        return
      }

      // 检查 onSend 方法是否存在
      if (typeof chatContext.onSend !== 'function') {
        console.error('onSend 方法不存在')
        sendResponse({
          code: '1',
          error: 'onSend 方法不存在',
        })
        return
      }

      // 使用 onSend 方法发起翻译请求
      const payload = {
        allowChange: false,
        agentId: 'translate',
        conversationID: conversationID,
        UserID: getUserId(),
        ResponseMode: 'blocking'
      }

      console.log('使用 onSend 发起翻译请求:', {
        query,
        payload
      })

      const result = chatContext.onSend('text', query, payload)

      if (result !== false) {
        console.log('翻译请求已通过 onSend 发送:', query)
        sendResponse({
          code: '0',
          result: '翻译请求已发送，请查看聊天界面获取结果',
        })
      } else {
        console.error('onSend 方法返回 false，发送失败')
        sendResponse({
          code: '1',
          error: '发送翻译请求失败',
        })
      }
    } catch (error) {
      console.error('翻译请求发送错误:', error)
      sendResponse({
        code: '1',
        error: error.message || '翻译请求发送失败',
      })
    }
  }

  // UI交互模式的翻译处理函数
  const handleTranslateUI = useCallback(async (pageTitle: string) => {
    if (!props?.chatUiRef || !messageApiToUse) {
      console.error('UI交互模式需要提供chatUiRef和messageApi参数')
      return
    }

    const handleStartTranslate = (currentTab: chrome.tabs.Tab) => {
      reportLog(pageTitle)
      chrome.tabs.sendMessage(currentTab.id!, {
        type: MessageType.START_TRANSLATE,
      })
    }

    const currentTab = await getCurrentTab(messageApiToUse)
    if (!currentTab?.id) {
      messageApiToUse.error('无法获取当前标签页')
      return
    }

    const isInjected = await checkContentScriptInjected(currentTab.id)
    if (!isInjected) {
      // 如果未注入，刷新页面
      await refreshCurrentPage(currentTab.id)
      // 监听页面加载完成事件
      chrome.tabs.onUpdated.addListener(
        async function listener(tabId, changeInfo) {
          if (tabId === currentTab.id && changeInfo.status === 'complete') {
            // 页面加载完成后，移除监听器
            chrome.tabs.onUpdated.removeListener(listener)

            // 确保content script注入完成
            const isInjectedAfterReload = await checkContentScriptInjected(
              currentTab.id!
            )
            if (isInjectedAfterReload) {
              // content script已注入，继续执行翻译
              handleStartTranslate(currentTab)
            } else {
              messageApiToUse.error('翻译功能注入失败，请手动刷新页面后重试')
            }
          }
        }
      )
      return
    }
    handleStartTranslate(currentTab)
  }, [props?.chatUiRef, messageApiToUse, reportLog, checkContentScriptInjected, refreshCurrentPage, getCurrentTab])

  useEffect(() => {
    ipConnectivity(messageApiToUse)
    const messageHandler = (message: any, _sender: any, sendResponse: any) => {
      console.log('收到消息:', message);

      if (message.type === MessageType.CREATE_CONVERSATION) {
        createConversation(sendResponse)
      } else if (message.type === MessageType.BATCH_TRANSLATE) {
        const { query, conversationID } = message.data
        handleTranslateAPI(query, conversationID, sendResponse)
      } else if (message.type === MessageType.SINGLE_TRANSLATE) {
        const { query, conversationID } = message.data
        handleTranslateAPI(query, conversationID, sendResponse)
      }
      return true
    }

    chrome.runtime.onMessage.addListener(messageHandler)

    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(messageHandler)
    }
  }, [messageApiToUse, props?.chatUiRef])

  // 根据使用模式返回不同的结果
  if (props?.chatUiRef && props?.messageApi) {
    // UI交互模式
    return {
      handleTranslate: handleTranslateUI
    }
  } else {
    // API模式
    return {
      contextHolder
    }
  }
}

// 导出类型和hook
export type { UseTranslateProps, UseTranslateReturn }
export default useTranslate
