import { useTranslate } from '@src/common/hooks'
import { useState } from 'react'

import Routing from './routes'
import './style.less'
import SelectionHandler from './components/SelectionHandler'
import { message } from 'antd'

export default () => {
  const { contextHolder } = useTranslate()
  const [selectedText, setSelectedText] = useState<string>('')
  const [textOperation, setTextOperation] = useState<string>('')

  // 处理从划词工具栏接收到的文本
  const handleTextSelected = (text: string, operation: string) => {
    setSelectedText(text)
    if (operation) {
      setTextOperation(operation)
      message.info(`正在${operation}选中的文本...`)
      // 这里可以根据不同的操作类型进行不同的处理
      // 例如，将操作类型和文本传递给相应的处理组件
    }
  }

  return (
    <div className="side-panel">
      <SelectionHandler onTextSelected={handleTextSelected} />
      <Routing selectedText={selectedText} textOperation={textOperation} />
      {contextHolder}
    </div>
  )
}
