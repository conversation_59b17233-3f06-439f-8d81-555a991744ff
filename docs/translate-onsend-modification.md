# useTranslate Hook 修改说明

## 修改概述

将 `useTranslate.tsx` 文件中的 `handleTranslateAPI` 方法修改为使用 `chatUiRef.current.chatContext.onSend` 方法来发起翻译会话请求，而不是使用原有的 HTTP API 请求方式。

## 主要修改内容

### 1. 更新接口类型定义

```typescript
interface UseTranslateProps {
  // 可选参数，用于UI交互模式和API模式
  chatUiRef?: React.RefObject<{
    chatContext?: {
      onSend: (type: string, val: string, payload?: object, transformedFiles?: any[]) => boolean | undefined;
      [key: string]: any;
    };
    [key: string]: any;
  }>
  messageApi?: MessageInstance
}
```

### 2. 重写 handleTranslateAPI 方法

原有实现：
- 使用 HTTP fetch 请求调用 `/ai/orchestration/api/v1/chat_query` 接口
- 解析响应文本获取翻译结果
- 复杂的响应解析逻辑

新实现：
- 检查 `chatUiRef.current` 是否存在
- 检查 `chatContext` 是否存在
- 检查 `onSend` 方法是否存在
- 使用 `chatContext.onSend` 方法发起翻译请求
- 简化的错误处理逻辑

### 3. 新的 handleTranslateAPI 实现

```typescript
const handleTranslateAPI = async (query: any, conversationID: any, sendResponse: any) => {
  try {
    // 检查 chatUiRef 是否存在
    if (!props?.chatUiRef?.current) {
      console.error('chatUiRef 未准备就绪')
      sendResponse({
        code: '1',
        error: 'chatUiRef 未准备就绪',
      })
      return
    }

    // 检查 chatContext 是否存在
    const chatContext = props.chatUiRef.current.chatContext
    if (!chatContext) {
      console.error('chatContext 未准备就绪')
      sendResponse({
        code: '1',
        error: 'chatContext 未准备就绪',
      })
      return
    }

    // 检查 onSend 方法是否存在
    if (typeof chatContext.onSend !== 'function') {
      console.error('onSend 方法不存在')
      sendResponse({
        code: '1',
        error: 'onSend 方法不存在',
      })
      return
    }

    // 使用 onSend 方法发起翻译请求
    const payload = {
      allowChange: false,
      agentId: 'translate',
      conversationID: conversationID,
      UserID: getUserId(),
      ResponseMode: 'blocking'
    }

    console.log('使用 onSend 发起翻译请求:', {
      query,
      payload
    })

    const result = chatContext.onSend('text', query, payload)
    
    if (result !== false) {
      console.log('翻译请求已通过 onSend 发送:', query)
      sendResponse({
        code: '0',
        result: '翻译请求已发送，请查看聊天界面获取结果',
      })
    } else {
      console.error('onSend 方法返回 false，发送失败')
      sendResponse({
        code: '1',
        error: '发送翻译请求失败',
      })
    }
  } catch (error) {
    console.error('翻译请求发送错误:', error)
    sendResponse({
      code: '1',
      error: error.message || '翻译请求发送失败',
    })
  }
}
```

### 4. 更新依赖数组

在 `useEffect` 的依赖数组中添加了 `props?.chatUiRef`，确保当 `chatUiRef` 改变时，消息处理器能够正确更新。

```typescript
useEffect(() => {
  // ... 消息处理逻辑
}, [messageApiToUse, props?.chatUiRef])
```

## onSend 方法接口说明

根据 `@ht/chatui` 库的类型定义：

```typescript
onSend: (type: string, val: string, payload?: object, transformedFiles?: UploadFile[]) => boolean | undefined;
```

参数说明：
- `type`: 消息类型，通常为 'text'
- `val`: 要发送的文本内容
- `payload`: 可选的负载对象，包含额外的配置信息
- `transformedFiles`: 可选的文件数组

返回值：
- `boolean | undefined`: 成功返回 `true` 或 `undefined`，失败返回 `false`

## 优势

1. **更直接的集成**: 直接使用聊天组件的内置方法，避免了复杂的 HTTP 请求处理
2. **更好的错误处理**: 简化的错误处理逻辑，更容易调试
3. **更好的类型安全**: 使用 TypeScript 类型定义，提供更好的类型检查
4. **更好的用户体验**: 翻译请求直接显示在聊天界面中，用户可以看到完整的对话流程

## 注意事项

1. 确保在调用 `handleTranslateAPI` 之前，`chatUiRef` 已经正确初始化
2. 确保 `@ht/chatui` 组件已经完全加载并且 `chatContext` 可用
3. 翻译结果将直接显示在聊天界面中，而不是通过 `sendResponse` 返回具体的翻译文本
